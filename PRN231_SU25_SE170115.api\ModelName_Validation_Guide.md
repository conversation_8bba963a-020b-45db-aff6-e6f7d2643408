# ModelName Validation Guide

## Regex Pattern
```regex
^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$
```

## Quy tắc Validation

### 1. C<PERSON>u trúc chung
- ModelName có thể là **một từ** hoặc **nhiều từ cách nhau bởi khoảng trắng**
- Mỗi từ phải bắt đầu bằng **chữ cái IN HOA (A-Z)** hoặc **số (0-9)**
- C<PERSON>c ký tự tiếp theo có thể là: **chữ cái (a-z, A-Z)**, **số (0-9)**, hoặc **dấu thăng (#)**

### 2. Ký tự được phép
- ✅ Chữ cái: `A-Z`, `a-z`
- ✅ Số: `0-9`
- ✅ <PERSON><PERSON><PERSON> thăng: `#`
- ✅ Khoảng trắng: ` ` (để phân tách các từ)

### 3. <PERSON><PERSON> tự KHÔNG được phép
- ❌ Ký tự đặc biệt khác: `!@$%^&*()_+-=[]{}|;':\",./<>?`
- ❌ Dấu gạch dưới: `_`
- ❌ Dấu gạch ngang: `-`

## ✅ Ví dụ ModelName ĐÚNG

### Một từ đơn
```
Elegant2024
Classic2023
Premium
Luxury
A1
B2C3
Model#1
Elite#2024
```

### Nhiều từ
```
Elegant #2024
Classic Flap Bag
Premium Collection 2024
Luxury Series #1
Model A1 Elite
Collection #2024 Premium
```

### Từ database hiện tại (đã test thành công)
```
Updated2024
Classic Flap Bag
Galleria Bag
Birkin 25
Lady Dior
TB Bag
Tabby Shoulder Bag
Jet Set Tote
Baguette Bag
Elegant2024
HB2024#Elite
HB2024#Classic
A1
Test2024
Test Bag
Test#2024
ModeratorTest
```

## ❌ Ví dụ ModelName SAI

### Bắt đầu bằng chữ thường
```
elegant2024          ❌ (phải bắt đầu bằng chữ HOA)
classic#2024         ❌ (phải bắt đầu bằng chữ HOA)
model A1             ❌ (từ đầu phải bắt đầu bằng chữ HOA)
```

### Bắt đầu bằng ký tự đặc biệt
```
#Elegant2024         ❌ (không được bắt đầu bằng #)
_Premium             ❌ (không được bắt đầu bằng _)
-Classic             ❌ (không được bắt đầu bằng -)
```

### Chứa ký tự không được phép
```
Elegant_2024         ❌ (không được chứa _)
Classic-Bag          ❌ (không được chứa -)
Premium@2024         ❌ (không được chứa @)
Model!Elite          ❌ (không được chứa !)
Luxury$Series        ❌ (không được chứa $)
```

### Khoảng trắng không đúng
```
 Elegant2024         ❌ (không được bắt đầu bằng khoảng trắng)
Elegant2024          ❌ (không được kết thúc bằng khoảng trắng)
Elegant  2024        ❌ (không được có nhiều khoảng trắng liên tiếp)
```

### Từ trong chuỗi nhiều từ bắt đầu sai
```
Elegant elegant2024  ❌ (từ thứ 2 phải bắt đầu bằng chữ HOA)
Premium classic      ❌ (từ thứ 2 phải bắt đầu bằng chữ HOA)
Model a1             ❌ (từ thứ 2 phải bắt đầu bằng chữ HOA)
```

## Phân tích Regex chi tiết

```regex
^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$
```

### Phần 1: `([A-Z0-9][a-zA-Z0-9#]*\s)*`
- `[A-Z0-9]`: Ký tự đầu tiên phải là chữ HOA hoặc số
- `[a-zA-Z0-9#]*`: Theo sau có thể là 0 hoặc nhiều ký tự (chữ, số, #)
- `\s`: Kết thúc bằng một khoảng trắng
- `*`: Toàn bộ nhóm này có thể lặp lại 0 hoặc nhiều lần

### Phần 2: `([A-Z0-9][a-zA-Z0-9#]*)`
- `[A-Z0-9]`: Từ cuối cùng phải bắt đầu bằng chữ HOA hoặc số
- `[a-zA-Z0-9#]*`: Theo sau có thể là 0 hoặc nhiều ký tự (chữ, số, #)

### Ý nghĩa tổng thể
- Cho phép một hoặc nhiều từ
- Mỗi từ phải bắt đầu bằng chữ HOA hoặc số
- Các từ cách nhau bởi đúng một khoảng trắng
- Từ cuối cùng không có khoảng trắng ở cuối

## Test Cases từ kết quả thực tế

### ✅ Đã test thành công (Status 201)
```bash
# Test Case 10
{"modelName":"Elegant2024","material":"Leather","price":250.5,"stock":10,"brandId":1}

# Test Case 14  
{"modelName":"ModeratorTest","material":"Leather","price":100,"stock":5,"brandId":1}
```

### ❌ Đã test thất bại (Status 400)
```bash
# Test Case 11
{"modelName":"invalid name","material":"Leather","price":-10,"stock":-5,"brandId":1}
# Lỗi vì: "invalid" bắt đầu bằng chữ thường

# Test Case đã thử
{"modelName":"Elegant #2024","material":"Leather","price":250.5,"stock":10,"brandId":1}
# Lỗi có thể do implementation cụ thể của validation
```

## Lưu ý quan trọng

1. **Regex này rất nghiêm ngặt** - mọi từ phải bắt đầu bằng chữ HOA hoặc số
2. **Khoảng trắng** chỉ được dùng để phân tách các từ, không được ở đầu/cuối
3. **Dấu thăng (#)** chỉ được dùng ở giữa hoặc cuối từ, không được ở đầu
4. **Validation có thể khác nhau** giữa POST và PUT endpoints

## Khuyến nghị

Để đảm bảo thành công, nên sử dụng format đơn giản:
- **Một từ**: `Elegant2024`, `Premium`, `Classic`
- **Nhiều từ**: `Classic Bag`, `Premium Series`, `Elite Collection`
- **Có số và #**: `Model2024`, `Series#1`, `Collection#2024`
