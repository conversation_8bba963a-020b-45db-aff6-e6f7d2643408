# Hướng dẫn Test Search API trên Swagger UI

## 🚨 Vấn đề thường gặp khi test Search trên Swagger

### **Vấn đề 1: Authentication**
❌ **Lỗi**: 401 Unauthorized
✅ **Giải pháp**: <PERSON><PERSON><PERSON> authenticate trước khi test search

### **Vấn đề 2: OData Parameters phức tạp**
❌ **Lỗi**: Swagger hiển thị quá nhiều OData parameters khó hiểu
✅ **Giải pháp**: Chỉ cần điền các parameters cần thiết

### **Vấn đề 3: URL Encoding**
❌ **Lỗi**: Special characters không được encode đúng
✅ **Giải pháp**: Swagger tự động encode, hoặc encode thủ công

## 📋 Hướng dẫn từng bước

### **Bước 1: Mở Swagger UI**
```
http://localhost:5038/swagger
```

### **Bước 2: Authenticate**
1. Click nút **"Authorize"** ở góc trên bên phải
2. <PERSON>gin để lấy token:
   ```
   POST /api/auth
   {
     "email": "<EMAIL>",
     "password": "123456"
   }
   ```
3. Copy token từ response
4. Paste vào ô "Value" với format: `Bearer YOUR_TOKEN_HERE`
5. Click **"Authorize"**

### **Bước 3: Test Search Endpoint**
1. Tìm endpoint **GET /api/Handbags/search**
2. Click **"Try it out"**
3. Điền parameters (xem chi tiết bên dưới)
4. Click **"Execute"**

## 🔍 Cách điền Parameters cho Search

### **Cách 1: Basic Search (Đơn giản nhất)**

#### **Search theo modelName:**
- **modelName**: `Classic`
- **material**: để trống
- **Các OData parameters**: để trống tất cả

#### **Search theo material:**
- **modelName**: để trống  
- **material**: `Leather`
- **Các OData parameters**: để trống tất cả

#### **Search kết hợp:**
- **modelName**: `HB2024`
- **material**: `Leather`
- **Các OData parameters**: để trống tất cả

### **Cách 2: OData Search (Nâng cao)**

#### **Chỉ điền vào ô "$filter":**

**Contains search:**
```
contains(modelName,'Bag')
```

**StartsWith search:**
```
startswith(modelName,'Test')
```

**EndsWith search:**
```
endswith(modelName,'2024')
```

**Equals search:**
```
material eq 'Canvas'
```

**Greater than search:**
```
price gt 5000
```

**Combined search:**
```
contains(modelName,'HB2024') and material eq 'Leather'
```

## ⚠️ Lưu ý quan trọng

### **1. Chỉ điền parameters cần thiết**
- Không cần điền tất cả OData parameters
- Để trống những gì không dùng
- Swagger sẽ tự động bỏ qua parameters trống

### **2. Format OData Filter đúng**
- Dùng single quotes cho string: `'Leather'`
- Không dùng double quotes: `"Leather"` ❌
- Functions: `contains()`, `startswith()`, `endswith()`
- Operators: `eq`, `gt`, `lt`, `and`, `or`

### **3. URL Encoding**
- Swagger tự động encode
- Nếu không work, thử encode thủ công:
  - Space: `%20`
  - Single quote: `%27`
  - Hash: `%23`

## 🐛 Troubleshooting

### **Lỗi 401 Unauthorized**
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```
**Giải pháp**: Kiểm tra lại authentication, token có thể đã expire

### **Lỗi 400 Bad Request**
```json
{
  "errorCode": "HB40001", 
  "message": "Missing/invalid input"
}
```
**Giải pháp**: Kiểm tra format OData filter, có thể syntax sai

### **Không có kết quả (Empty array)**
```json
[]
```
**Giải pháp**: Bình thường, có nghĩa là không tìm thấy kết quả phù hợp

### **Swagger UI không hiển thị đúng**
**Giải pháp**: 
1. Refresh browser
2. Clear cache
3. Restart server
4. Kiểm tra console browser có lỗi không

## 📝 Test Cases mẫu cho Swagger

### **Test Case 1: Basic Search**
```
GET /api/Handbags/search
Parameters:
- modelName: Classic
- material: (empty)
- All OData params: (empty)
```

### **Test Case 2: OData Filter**
```
GET /api/Handbags/search  
Parameters:
- modelName: (empty)
- material: (empty)
- $filter: contains(modelName,'Bag')
- Other OData params: (empty)
```

### **Test Case 3: Combined**
```
GET /api/Handbags/search
Parameters:
- modelName: HB2024
- material: Leather
- All OData params: (empty)
```

## 🎯 Expected Response Format

```json
[
  {
    "brandName": "Louis Vuitton",
    "handbags": [
      {
        "handbagId": 12,
        "modelName": "Elegant2024",
        "material": "Leather",
        "color": null,
        "price": 250.50,
        "stock": 10,
        "releaseDate": "2025-07-17",
        "brandName": "Louis Vuitton",
        "country": "France",
        "foundedYear": 1854,
        "website": "https://www.louisvuitton.com"
      }
    ]
  }
]
```

## 🔧 Alternative: Test bằng curl

Nếu Swagger không work, có thể test bằng curl:

```bash
# Basic search
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Classic" \
  -H "Authorization: Bearer YOUR_TOKEN"

# OData search  
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=contains(modelName,'Bag')" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 💡 Tips

1. **Luôn authenticate trước** khi test bất kỳ endpoint nào
2. **Bắt đầu với basic search** trước khi thử OData
3. **Kiểm tra Network tab** trong browser để xem request thực tế
4. **Copy curl command** từ Swagger để test ngoài browser
5. **Kiểm tra server logs** nếu có lỗi không rõ nguyên nhân
