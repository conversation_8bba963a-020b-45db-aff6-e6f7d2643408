# Debug Swagger Search Issues

## ✅ API hoạt động bình thường

Tôi đã test và xác nhận rằng Search API hoạt động hoàn toàn bình thường:

```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Classic" \
  -H "Authorization: Bearer [TOKEN]"
```

**Response**: Status 200 OK với kết quả chính xác.

## 🚨 Các vấn đề có thể gặp trên Swagger UI

### **1. Authentication Issues**

#### Vấn đề:
- Swagger UI không authenticate đúng
- Token expire
- Format Bearer token sai

#### Giải pháp:
1. **Login để lấy token mới:**
   ```json
   POST /api/auth
   {
     "email": "<EMAIL>", 
     "password": "123456"
   }
   ```

2. **Authenticate trong Swagger:**
   - Click nút "Authorize" 
   - Nhập: `Bearer YOUR_TOKEN_HERE`
   - <PERSON><PERSON><PERSON> bả<PERSON> có từ "Bearer " ở đầu

3. **Ki<PERSON>m tra token còn hạn:**
   - Token có thời hạn (exp claim)
   - Nếu expire, phải login lại

### **2. OData Parameters Confusion**

#### Vấn đề:
Swagger hiển thị rất nhiều OData parameters phức tạp:
- odataOptions
- $filter, $orderby, $top, $skip, etc.
- Gây confusion cho user

#### Giải pháp:
**Chỉ cần điền những gì cần thiết:**

**Cách 1: Basic Search (Đơn giản nhất)**
- `modelName`: Classic
- `material`: Leather  
- **Bỏ trống tất cả OData parameters**

**Cách 2: OData Filter**
- `modelName`: (trống)
- `material`: (trống)
- `$filter`: contains(modelName,'Bag')
- **Bỏ trống các OData parameters khác**

### **3. URL Encoding Issues**

#### Vấn đề:
- Special characters không được encode
- Single quotes trong OData filter
- Spaces trong search terms

#### Giải pháp:
**Swagger thường tự động encode, nhưng nếu không work:**
- Space: `%20`
- Single quote: `%27` 
- Hash: `%23`
- Dollar sign: `%24`

### **4. Browser/Cache Issues**

#### Vấn đề:
- Swagger UI cache cũ
- Browser cache
- JavaScript errors

#### Giải pháp:
1. **Hard refresh**: Ctrl+F5
2. **Clear browser cache**
3. **Open Developer Tools** → Check Console for errors
4. **Try incognito/private mode**

## 🔧 Step-by-step Swagger Testing

### **Bước 1: Mở Swagger**
```
http://localhost:5038/swagger
```

### **Bước 2: Authenticate**
1. Click **"Authorize"** button (🔒 icon)
2. Login first:
   - Expand **POST /api/auth**
   - Click **"Try it out"**
   - Enter credentials:
     ```json
     {
       "email": "<EMAIL>",
       "password": "123456"
     }
     ```
   - Click **"Execute"**
   - Copy the token from response

3. Go back to Authorization:
   - Paste token with "Bearer " prefix
   - Example: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - Click **"Authorize"**
   - Click **"Close"**

### **Bước 3: Test Search**
1. Find **GET /api/Handbags/search**
2. Click **"Try it out"**
3. **Simple test first:**
   - modelName: `Classic`
   - material: (leave empty)
   - All OData params: (leave empty)
4. Click **"Execute"**

### **Bước 4: Advanced OData Test**
1. Clear previous parameters
2. Leave modelName and material empty
3. Fill only `$filter`: `contains(modelName,'Bag')`
4. Click **"Execute"**

## 🐛 Common Error Messages

### **401 Unauthorized**
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```
**Fix**: Re-authenticate with fresh token

### **400 Bad Request** 
```json
{
  "errorCode": "HB40001",
  "message": "Missing/invalid input"
}
```
**Fix**: Check OData filter syntax

### **Empty Response**
```json
[]
```
**This is normal** - means no results found

## 🎯 Working Examples for Swagger

### **Example 1: Basic Search**
```
GET /api/Handbags/search
Parameters:
- modelName: Classic
- material: (empty)
- odataOptions: (empty)
- All other OData params: (empty)
```

### **Example 2: Material Search**
```
GET /api/Handbags/search  
Parameters:
- modelName: (empty)
- material: Leather
- All OData params: (empty)
```

### **Example 3: OData Contains**
```
GET /api/Handbags/search
Parameters:
- modelName: (empty)
- material: (empty)
- $filter: contains(modelName,'Bag')
- Other OData params: (empty)
```

### **Example 4: OData Price Filter**
```
GET /api/Handbags/search
Parameters:
- modelName: (empty)
- material: (empty)  
- $filter: price gt 1000
- Other OData params: (empty)
```

### **Example 5: Combined OData**
```
GET /api/Handbags/search
Parameters:
- modelName: (empty)
- material: (empty)
- $filter: contains(modelName,'HB2024') and material eq 'Leather'
- Other OData params: (empty)
```

## 💡 Pro Tips

1. **Always authenticate first** - Most common issue
2. **Start simple** - Use basic search before OData
3. **Check Network tab** - See actual HTTP request
4. **Copy as cURL** - Test outside Swagger if needed
5. **One parameter at a time** - Don't fill everything
6. **Use single quotes** in OData filters: `'Leather'` not `"Leather"`
7. **Leave unused params empty** - Don't try to fill everything

## ✅ SOLUTION: New Simple Search Endpoint

**I've added a new endpoint that works perfectly with Swagger UI:**

### **GET /api/handbags/search-simple**

This endpoint:
- ✅ **No OData parameters** - avoids 414 URI Too Long error
- ✅ **Simple parameters**: only `modelName` and `material`
- ✅ **Works in Swagger UI** - no URL length issues
- ✅ **Same functionality** - returns grouped results by brand

### **How to use in Swagger:**

1. **Authenticate first** (get Bearer token)
2. **Find GET /api/Handbags/search-simple**
3. **Fill only what you need:**
   - `modelName`: Classic
   - `material`: Leather (or leave empty)
4. **Click Execute**

### **Test Results:**
```bash
curl -X GET "http://localhost:5038/api/handbags/search-simple?modelName=Classic" \
  -H "Authorization: Bearer [TOKEN]"
```

**Response**: Status 200 ✅
```json
[
  {
    "brandName": "Chanel",
    "handbags": [
      {
        "handbagId": 3,
        "modelName": "Classic Flap Bag",
        "material": "Leather",
        "color": "Black",
        "price": 7500.00,
        "stock": 20,
        "releaseDate": "2023-12-20",
        "brandName": "Chanel",
        "country": "France",
        "foundedYear": 1910,
        "website": "https://www.chanel.com"
      }
    ]
  }
]
```

## 🔍 If You Still Want OData Features

**Use cURL or Postman for advanced OData:**

### **1. Use cURL directly:**
```bash
# Get fresh token
curl -X POST "http://localhost:5038/api/auth" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'

# Test OData search
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=contains(modelName,'Bag')" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **2. Use Postman:**
- Import the API
- Set Authorization header
- Test endpoints with OData filters

### **3. Use browser directly:**
```
http://localhost:5038/api/handbags/search?modelName=Classic
```
(Won't work due to CORS and auth, but good for testing URL format)

## 📋 Checklist

Before reporting Swagger issues, verify:

- [ ] Server is running (http://localhost:5038)
- [ ] Swagger UI loads (http://localhost:5038/swagger)
- [ ] Can authenticate successfully
- [ ] Token is valid and not expired
- [ ] Using correct parameter names
- [ ] OData syntax is correct
- [ ] Browser console shows no errors
- [ ] API works with cURL
- [ ] Tried hard refresh/clear cache

If all above are OK and Swagger still doesn't work, the issue might be with Swagger UI configuration or OData integration with Swagger.
