﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace Model
{
    public class CreateHandBagModel
    {
        public string ModelName { get; set; }

        public string Material { get; set; } 

        public decimal Price { get; set; }
        public int Stock { get; set; } 
        public int BrandId { get; set; }
    }

    public class UpdateHandBagModel
    {
        public string ModelName { get; set; }

        public string Material { get; set; }
        public string Color { get; set; }
        public decimal Price { get; set; }
        public int Stock { get; set; }
        public int BrandId { get; set; }
    }
}
