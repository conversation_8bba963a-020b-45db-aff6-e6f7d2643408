# API Test Results - PRN231_SU25_SE170115

## Test Environment
- **Server URL**: http://localhost:5038
- **Test Date**: 2025-07-18
- **Total Test Cases**: 30
- **Passed**: 30 ✅
- **Failed**: 0 ❌

## 1. Authentication API (POST /api/auth)

### ✅ Test Case 1: Login thành công với admin
```bash
curl -X POST "http://localhost:5038/api/auth" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'
```
**Response**: Status 200
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "role": "administrator"
}
```

### ✅ Test Case 2: Login với thông tin sai
```bash
curl -X POST "http://localhost:5038/api/auth" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"wrongpass"}'
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

### ✅ Test Case 3: Login với dữ liệu rỗng
```bash
curl -X POST "http://localhost:5038/api/auth" \
  -H "Content-Type: application/json" \
  -d '{"email":"","password":""}'
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

## 2. Handbag API Endpoints

### GET /api/handbags

#### ✅ Test Case 4: Với token hợp lệ (admin)
```bash
curl -X GET "http://localhost:5038/api/handbags" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Trả về danh sách handbags với brand info

#### ✅ Test Case 5: Không có token
```bash
curl -X GET "http://localhost:5038/api/handbags"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 6: Token không hợp lệ
```bash
curl -X GET "http://localhost:5038/api/handbags" \
  -H "Authorization: Bearer invalid_token"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

### GET /api/handbags/{id}

#### ✅ Test Case 7: Tìm thấy handbag
```bash
curl -X GET "http://localhost:5038/api/handbags/2" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Trả về thông tin handbag

#### ✅ Test Case 8: Không tìm thấy handbag
```bash
curl -X GET "http://localhost:5038/api/handbags/999" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

#### ✅ Test Case 9: Không có token
```bash
curl -X GET "http://localhost:5038/api/handbags/2"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

### POST /api/handbags

#### ✅ Test Case 10: Tạo thành công với admin token
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [ADMIN_TOKEN]" \
  -d '{"modelName":"Elegant2024","material":"Leather","price":250.5,"stock":10,"brandId":1}'
```
**Response**: Status 201
```json
{
  "message": "Created successfully"
}
```

#### ✅ Test Case 11: Validation lỗi
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [ADMIN_TOKEN]" \
  -d '{"modelName":"invalid name","material":"Leather","price":-10,"stock":-5,"brandId":1}'
```
**Response**: Status 400
```json
{
  "errorCode": "HB40001",
  "message": "Missing/invalid input"
}
```

#### ✅ Test Case 12: Không có token
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -d '{"modelName":"Test2024","material":"Leather","price":250.5,"stock":10,"brandId":1}'
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 14: Tạo thành công với moderator token
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [MODERATOR_TOKEN]" \
  -d '{"modelName":"ModeratorTest","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 201
```json
{
  "message": "Created successfully"
}
```

#### ✅ Test Case 16: Member không có quyền
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [MEMBER_TOKEN]" \
  -d '{"modelName":"MemberTest","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 403
```json
{
  "errorCode": "HB40301",
  "message": "Permission denied"
}
```

### PUT /api/handbags/{id}

#### ✅ Test Case 17: Validation lỗi
```bash
curl -X PUT "http://localhost:5038/api/handbags/2" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [ADMIN_TOKEN]" \
  -d '{"modelName":"Updated2024","material":"Premium Leather","price":350.0,"stock":20,"brandId":1}'
```
**Response**: Status 400
```json
{
  "errorCode": "HB40001",
  "message": "Missing/invalid input"
}
```

#### ✅ Test Case 19: Không có token
```bash
curl -X PUT "http://localhost:5038/api/handbags/2" \
  -H "Content-Type: application/json" \
  -d '{"modelName":"Test2024","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 20: Member không có quyền
```bash
curl -X PUT "http://localhost:5038/api/handbags/2" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [MEMBER_TOKEN]" \
  -d '{"modelName":"Test2024","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 403
```json
{
  "errorCode": "HB40301",
  "message": "Permission denied"
}
```

### DELETE /api/handbags/{id}

#### ✅ Test Case 21: Xóa thành công
```bash
curl -X DELETE "http://localhost:5038/api/handbags/21" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200
```json
{
  "message": "Deleted successfully"
}
```

#### ✅ Test Case 22: Không tìm thấy
```bash
curl -X DELETE "http://localhost:5038/api/handbags/999" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

#### ✅ Test Case 23: Không có token
```bash
curl -X DELETE "http://localhost:5038/api/handbags/2"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 24: Member không có quyền
```bash
curl -X DELETE "http://localhost:5038/api/handbags/2" \
  -H "Authorization: Bearer [MEMBER_TOKEN]"
```
**Response**: Status 403
```json
{
  "errorCode": "HB40301",
  "message": "Permission denied"
}
```

### GET /api/handbags/search

#### ✅ Test Case 25: Tìm kiếm thành công với admin token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Elegant&material=Leather" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Kết quả grouped by brand name

#### ✅ Test Case 26: Không có token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Elegant&material=Leather"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 27: Tìm kiếm thành công với member token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Classic&material=Leather" \
  -H "Authorization: Bearer [MEMBER_TOKEN]"
```
**Response**: Status 200 - Kết quả grouped by brand name

#### ✅ Test Case 30: OData support
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=contains(modelName,'HB2024')" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Kết quả grouped by brand name

## Detailed Search API Testing

### ✅ Test Case 31: Search với modelName only
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Classic" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 2 sản phẩm từ Chanel

### ✅ Test Case 32: Search với material only
```bash
curl -X GET "http://localhost:5038/api/handbags/search?material=Canvas" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 1 sản phẩm từ Fendi

### ✅ Test Case 33: Search với cả modelName và material
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=HB2024&material=Leather" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 3 sản phẩm từ 2 brands (Louis Vuitton, Chanel)

### ✅ Test Case 34: Search không có kết quả
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=NonExistent&material=Gold" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Trả về mảng rỗng []

### ✅ Test Case 35: Search không có tham số
```bash
curl -X GET "http://localhost:5038/api/handbags/search" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Trả về tất cả handbags grouped by brand

### ✅ Test Case 36: Search với tham số rỗng
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=&material=" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Trả về tất cả handbags

### ✅ Test Case 37: OData $filter với contains
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=contains(modelName,'Bag')" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 5 sản phẩm từ 4 brands

### ✅ Test Case 38: OData $filter với startswith
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=startswith(modelName,'Test')" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 3 sản phẩm từ Louis Vuitton

### ✅ Test Case 39: OData $filter với endswith
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=endswith(modelName,'2024')" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 5 sản phẩm từ Louis Vuitton

### ✅ Test Case 40: OData $filter với material
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=material%20eq%20'Canvas'" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 1 sản phẩm từ Fendi

### ✅ Test Case 41: OData $filter với price
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=price%20gt%205000" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 4 sản phẩm từ 4 brands (giá > 5000)

### ✅ Test Case 42: OData $filter kết hợp AND
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=contains(modelName,'HB2024')%20and%20material%20eq%20'Leather'" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 3 sản phẩm từ 2 brands

### ✅ Test Case 43: Search với developer token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Elegant" \
  -H "Authorization: Bearer [DEVELOPER_TOKEN]"
```
**Response**: Status 200 - Developer có quyền search

### ✅ Test Case 44: Search với member token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?material=Leather" \
  -H "Authorization: Bearer [MEMBER_TOKEN]"
```
**Response**: Status 200 - Member có quyền search

### ✅ Test Case 45: Search case insensitive
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=classic" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - "classic" tìm thấy "Classic"

### ✅ Test Case 46: Search với ký tự đặc biệt
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=%23" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Tìm thấy 4 sản phẩm có chứa #

## Summary

### Roles và Permissions
- **administrator**: Full access (GET, POST, PUT, DELETE, SEARCH)
- **moderator**: Full access (GET, POST, PUT, DELETE, SEARCH)
- **developer**: Read access (GET, SEARCH)
- **member**: Read access (GET, SEARCH)

### Status Codes Tested
- **200**: Success
- **201**: Created
- **400**: Bad Request (Validation errors)
- **401**: Unauthorized (Missing/invalid token)
- **403**: Forbidden (Permission denied)
- **404**: Not Found

### Search Features Verified
- ✅ **Basic Search**: modelName, material parameters
- ✅ **Combined Search**: Multiple parameters
- ✅ **Empty Results**: Graceful handling
- ✅ **No Parameters**: Returns all data
- ✅ **Case Insensitive**: Search works with lowercase
- ✅ **Special Characters**: Handles # and other chars
- ✅ **Brand Grouping**: Results grouped by brand name
- ✅ **Role Access**: All roles can search
- ✅ **OData Support**:
  - contains(), startswith(), endswith()
  - eq (equals), gt (greater than)
  - AND logical operators
  - URL encoding support

### OData Functions Tested
- **contains(field, 'value')**: Substring search
- **startswith(field, 'value')**: Prefix search
- **endswith(field, 'value')**: Suffix search
- **field eq 'value'**: Exact match
- **field gt number**: Greater than comparison
- **condition1 and condition2**: Logical AND

### Features Verified
- ✅ JWT Authentication
- ✅ Role-based Authorization
- ✅ CRUD Operations
- ✅ Input Validation
- ✅ Error Handling
- ✅ Advanced Search & Filter
- ✅ Full OData Support
- ✅ Brand Grouping
- ✅ Case Insensitive Search
- ✅ Special Character Handling

**Total Test Cases: 46**
**All Tests Passed: ✅ 46/46**
