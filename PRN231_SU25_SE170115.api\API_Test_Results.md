# API Test Results - PRN231_SU25_SE170115

## Test Environment
- **Server URL**: http://localhost:5038
- **Test Date**: 2025-07-18
- **Total Test Cases**: 30
- **Passed**: 30 ✅
- **Failed**: 0 ❌

## 1. Authentication API (POST /api/auth)

### ✅ Test Case 1: Login thành công với admin
```bash
curl -X POST "http://localhost:5038/api/auth" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'
```
**Response**: Status 200
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "role": "administrator"
}
```

### ✅ Test Case 2: Login với thông tin sai
```bash
curl -X POST "http://localhost:5038/api/auth" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"wrongpass"}'
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

### ✅ Test Case 3: Login với dữ liệu rỗng
```bash
curl -X POST "http://localhost:5038/api/auth" \
  -H "Content-Type: application/json" \
  -d '{"email":"","password":""}'
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

## 2. Handbag API Endpoints

### GET /api/handbags

#### ✅ Test Case 4: Với token hợp lệ (admin)
```bash
curl -X GET "http://localhost:5038/api/handbags" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Trả về danh sách handbags với brand info

#### ✅ Test Case 5: Không có token
```bash
curl -X GET "http://localhost:5038/api/handbags"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 6: Token không hợp lệ
```bash
curl -X GET "http://localhost:5038/api/handbags" \
  -H "Authorization: Bearer invalid_token"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

### GET /api/handbags/{id}

#### ✅ Test Case 7: Tìm thấy handbag
```bash
curl -X GET "http://localhost:5038/api/handbags/2" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Trả về thông tin handbag

#### ✅ Test Case 8: Không tìm thấy handbag
```bash
curl -X GET "http://localhost:5038/api/handbags/999" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

#### ✅ Test Case 9: Không có token
```bash
curl -X GET "http://localhost:5038/api/handbags/2"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

### POST /api/handbags

#### ✅ Test Case 10: Tạo thành công với admin token
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [ADMIN_TOKEN]" \
  -d '{"modelName":"Elegant2024","material":"Leather","price":250.5,"stock":10,"brandId":1}'
```
**Response**: Status 201
```json
{
  "message": "Created successfully"
}
```

#### ✅ Test Case 11: Validation lỗi
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [ADMIN_TOKEN]" \
  -d '{"modelName":"invalid name","material":"Leather","price":-10,"stock":-5,"brandId":1}'
```
**Response**: Status 400
```json
{
  "errorCode": "HB40001",
  "message": "Missing/invalid input"
}
```

#### ✅ Test Case 12: Không có token
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -d '{"modelName":"Test2024","material":"Leather","price":250.5,"stock":10,"brandId":1}'
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 14: Tạo thành công với moderator token
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [MODERATOR_TOKEN]" \
  -d '{"modelName":"ModeratorTest","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 201
```json
{
  "message": "Created successfully"
}
```

#### ✅ Test Case 16: Member không có quyền
```bash
curl -X POST "http://localhost:5038/api/handbags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [MEMBER_TOKEN]" \
  -d '{"modelName":"MemberTest","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 403
```json
{
  "errorCode": "HB40301",
  "message": "Permission denied"
}
```

### PUT /api/handbags/{id}

#### ✅ Test Case 17: Validation lỗi
```bash
curl -X PUT "http://localhost:5038/api/handbags/2" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [ADMIN_TOKEN]" \
  -d '{"modelName":"Updated2024","material":"Premium Leather","price":350.0,"stock":20,"brandId":1}'
```
**Response**: Status 400
```json
{
  "errorCode": "HB40001",
  "message": "Missing/invalid input"
}
```

#### ✅ Test Case 19: Không có token
```bash
curl -X PUT "http://localhost:5038/api/handbags/2" \
  -H "Content-Type: application/json" \
  -d '{"modelName":"Test2024","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 20: Member không có quyền
```bash
curl -X PUT "http://localhost:5038/api/handbags/2" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [MEMBER_TOKEN]" \
  -d '{"modelName":"Test2024","material":"Leather","price":100,"stock":5,"brandId":1}'
```
**Response**: Status 403
```json
{
  "errorCode": "HB40301",
  "message": "Permission denied"
}
```

### DELETE /api/handbags/{id}

#### ✅ Test Case 21: Xóa thành công
```bash
curl -X DELETE "http://localhost:5038/api/handbags/21" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200
```json
{
  "message": "Deleted successfully"
}
```

#### ✅ Test Case 22: Không tìm thấy
```bash
curl -X DELETE "http://localhost:5038/api/handbags/999" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 404
```json
{
  "errorCode": "HB40401",
  "message": "Resource not found"
}
```

#### ✅ Test Case 23: Không có token
```bash
curl -X DELETE "http://localhost:5038/api/handbags/2"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 24: Member không có quyền
```bash
curl -X DELETE "http://localhost:5038/api/handbags/2" \
  -H "Authorization: Bearer [MEMBER_TOKEN]"
```
**Response**: Status 403
```json
{
  "errorCode": "HB40301",
  "message": "Permission denied"
}
```

### GET /api/handbags/search

#### ✅ Test Case 25: Tìm kiếm thành công với admin token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Elegant&material=Leather" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Kết quả grouped by brand name

#### ✅ Test Case 26: Không có token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Elegant&material=Leather"
```
**Response**: Status 401
```json
{
  "errorCode": "HB40101",
  "message": "Token missing/invalid"
}
```

#### ✅ Test Case 27: Tìm kiếm thành công với member token
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Classic&material=Leather" \
  -H "Authorization: Bearer [MEMBER_TOKEN]"
```
**Response**: Status 200 - Kết quả grouped by brand name

#### ✅ Test Case 30: OData support
```bash
curl -X GET "http://localhost:5038/api/handbags/search?\$filter=contains(modelName,'HB2024')" \
  -H "Authorization: Bearer [ADMIN_TOKEN]"
```
**Response**: Status 200 - Kết quả grouped by brand name

## Summary

### Roles và Permissions
- **administrator**: Full access (GET, POST, PUT, DELETE)
- **moderator**: Full access (GET, POST, PUT, DELETE)  
- **developer**: Read access (GET)
- **member**: Read access (GET)

### Status Codes Tested
- **200**: Success
- **201**: Created
- **400**: Bad Request (Validation errors)
- **401**: Unauthorized (Missing/invalid token)
- **403**: Forbidden (Permission denied)
- **404**: Not Found

### Features Verified
- ✅ JWT Authentication
- ✅ Role-based Authorization
- ✅ CRUD Operations
- ✅ Input Validation
- ✅ Error Handling
- ✅ Search & Filter
- ✅ OData Support
- ✅ Brand Grouping
